import React, { useState, useMemo } from 'react';
import { Dialog } from '@/components/common/dialog';
import AssignSkillModal from './assignSkillModal';
import { Input } from '@/components/common/input';
import {
  getSkillLevelColor,
  getSkillLevelName,
} from '@/utils/skillLevelColors';
import SkillLevelLegend from '@/components/skills/skillLevelLegend';

const mockEmployees = [
  { id: 'u1', name: '<PERSON>' },
  { id: 'u2', name: '<PERSON>' },
  { id: 'u3', name: '<PERSON>' },
  { id: 'u4', name: '<PERSON>' },
  { id: 'u5', name: '<PERSON>' },
  { id: 'u6', name: '<PERSON>' },
  { id: 'u7', name: '<PERSON>' },
  { id: 'u8', name: '<PERSON> 8' },
  { id: 'u9', name: '<PERSON> 9' },
  { id: 'u10', name: '<PERSON> 10' },
  { id: 'u11', name: '<PERSON> 11' },
  { id: 'u12', name: '<PERSON> 12' },
  { id: 'u13', name: '<PERSON> 13' },
  { id: 'u14', name: '<PERSON> 14' },
  { id: 'u15', name: 'Sophia Garcia 15' },
];

const mockSkillCategories = [
  {
    category: 'Quality',
    skills: [
      { id: 's1', name: 'Quality Management Systems' },
      { id: 's2', name: 'Statistical Process Control' },
      { id: 's3', name: 'Root Cause Analysis' },
    ],
  },
  {
    category: 'Safety',
    skills: [
      { id: 's4', name: 'Safety Protocols' },
      { id: 's5', name: 'Emergency Response' },
    ],
  },
];

const mockSkillLevels = [
  { employeeId: 'u1', skillId: 's1', level: 'L3' },
  { employeeId: 'u1', skillId: 's2', level: 'L4' },
  { employeeId: 'u2', skillId: 's3', level: 'L2' },
  { employeeId: 'u3', skillId: 's4', level: 'L4' },
  { employeeId: 'u4', skillId: 's5', level: 'L3' },
];

export default function SkillsMatrix() {
  const [selected, setSelected] = useState<{
    employeeId: string;
    skillId: string;
  } | null>(null);
  const [search, setSearch] = useState('');

  const getLevel = (employeeId: string, skillId: string) => {
    const found = mockSkillLevels.find(
      (l) => l.employeeId === employeeId && l.skillId === skillId,
    );
    return found ? found.level : '+';
  };

  const getLevelDisplay = (level: string) => {
    if (level === '+') return { level: '+', name: 'Not Assigned' };
    // Keep the "L" prefix for display
    return { level, name: getSkillLevelName(level.replace('L', '')) };
  };

  const getLevelStyles = (level: string) => {
    if (level === '+') {
      return 'bg-white-100 text-grey-300 border border-dashed border-grey-200';
    }
    const levelNumber = level.replace('L', '');
    const colors = getSkillLevelColor(levelNumber);
    return `${colors.bg} text-white font-medium`;
  };

  const filteredEmployees = useMemo(() => {
    return mockEmployees.filter((e) =>
      e.name.toLowerCase().includes(search.toLowerCase()),
    );
  }, [search]);

  const filteredSkillCategories = useMemo(() => {
    if (!search.trim()) return mockSkillCategories;
    return mockSkillCategories.map((cat) => ({
      ...cat,
      skills: cat.skills.filter((s) =>
        s.name.toLowerCase().includes(search.toLowerCase()),
      ),
    }));
  }, [search]);

  return (
    <div className="space-y-5">
      <div className="mt-4">
        <SkillLevelLegend />
      </div>
      <div className="flex flex-col sm:flex-row justify-between mb-4 gap-2 mt-4">
        <div className="flex items-center justify-between relative w-full">
          <Input
            placeholder="Search by employee or skill"
            className="w-[40vw] bg-white flex-auto rounded-lg border border-grey-100 py-2.5 px-3 text-black outline-none transition focus:border-primary active:border-primary"
            onChange={(e) => {
              setSearch(e.target.value);
            }}
            value={search}
          />
        </div>
      </div>

      {/* Desktop/Tablet Table */}
      <div className="hidden md:block rounded-lg border border-white-200 bg-white-100 shadow-sm max-h-[70vh] overflow-auto">
        <table className="min-w-full text-base border-collapse table-auto">
          <thead>
            {/* Header row: sticky at top */}
            <tr className="bg-white-200 text-left border border-grey-100 sticky top-0 z-40">
              {/* Skills column header (auto width) */}
              <th className="px-3 py-3 sticky left-0 z-50 bg-white-200">
                <span className="text-base font-semibold text-grey-300 truncate">
                  Skills
                </span>
              </th>
              {/* Employee columns */}
              {filteredEmployees.map((emp) => (
                <th
                  key={emp.id}
                  className="px-3 py-3 bg-white-200 border-b border-grey-100 min-w-[120px]"
                  title={emp.name}
                >
                  <span className="text-base font-semibold text-grey-300 truncate block max-w-[10rem] lg:max-w-[14rem]">
                    {emp.name}
                  </span>
                </th>
              ))}
            </tr>
          </thead>

          <tbody>
            {filteredSkillCategories.map((cat) => (
              <React.Fragment key={cat.category}>
                {/* Sticky Category Row */}
                <tr className="border-t border-white-200">
                  {/* Sticky first column */}
                  <td
                    className="px-3 py-2 text-dark-300 font-semibold sticky left-0 z-40 bg-white-50"
                    style={{ top: '48px' }}
                  >
                    {cat.category}
                  </td>
                  <td
                    colSpan={filteredEmployees.length}
                    className="bg-white-50"
                    style={{ top: '48px' }}
                  />
                </tr>

                {/* Skills rows */}
                {cat.skills.map((skill) => (
                  <tr key={skill.id} className="border-t border-white-200">
                    {/* Sticky skill name */}
                    <td className="p-2 sticky left-0 z-30 bg-white-100">
                      <span className="inline-block rounded-full bg-primary-100 text-primary-400 px-3 py-1 text-sm font-medium whitespace-nowrap">
                        {skill.name}
                      </span>
                    </td>

                    {/* Employee skill levels */}
                    {filteredEmployees.map((emp) => {
                      const level = getLevel(emp.id, skill.id);
                      const levelDisplay = getLevelDisplay(level);
                      const levelStyles = getLevelStyles(level);
                      return (
                        <td
                          key={emp.id}
                          className="p-2 text-center cursor-pointer hover:bg-white-50"
                          onClick={() =>
                            setSelected({
                              employeeId: emp.id,
                              skillId: skill.id,
                            })
                          }
                          title={`${emp.name} - ${levelDisplay.name}`}
                        >
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${levelStyles}`}
                          >
                            {levelDisplay.level}
                          </span>
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile List */}
      <div className="md:hidden space-y-3">
        {filteredSkillCategories.map((cat) => (
          <div
            key={cat.category}
            className="rounded-lg border border-white-200 bg-white-100 shadow-sm"
          >
            <div className="px-3 py-2 bg-white-50 text-dark-300 font-semibold rounded-t-lg sticky top-0 z-10">
              {cat.category}
            </div>
            <div className="divide-y divide-white-200">
              {cat.skills.map((skill) => (
                <div key={skill.id} className="p-3 space-y-2">
                  <div>
                    <span className="inline-block rounded-full bg-primary-100 text-primary-400 px-3 py-1 text-xs font-medium">
                      {skill.name}
                    </span>
                  </div>
                  <div className="flex gap-2 overflow-x-auto pb-1">
                    {filteredEmployees.map((emp) => {
                      const level = getLevel(emp.id, skill.id);
                      const levelDisplay = getLevelDisplay(level);
                      const levelStyles = getLevelStyles(level);
                      return (
                        <button
                          key={emp.id}
                          className="flex-shrink-0 inline-flex items-center gap-2 px-3 py-2 rounded-lg border border-white-200 bg-white-100 hover:bg-white-50"
                          onClick={() =>
                            setSelected({
                              employeeId: emp.id,
                              skillId: skill.id,
                            })
                          }
                          title={`${emp.name} - ${levelDisplay.name}`}
                        >
                          <span className="max-w-[8rem] truncate text-sm text-dark-200">
                            {emp.name}
                          </span>
                          <span
                            className={`px-2 py-0.5 rounded-full text-sm ${levelStyles}`}
                          >
                            {levelDisplay.level}
                          </span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {selected && (
        <Dialog open={!!selected} onOpenChange={() => setSelected(null)}>
          <AssignSkillModal
            employeeId={selected.employeeId}
            skillId={selected.skillId}
            onClose={() => setSelected(null)}
          />
        </Dialog>
      )}
    </div>
  );
}
