import AdvancedTable from '@/components/common/advancedTable';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import Layout from '@/components/common/sidebar/layout';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { Pencil, Plus, TrashIcon, UserPlus } from 'lucide-react';
import { toast } from 'react-toastify';
import Tabs from '@/components/common/tabs';
import DeleteModal from '@/components/common/modals/deleteModal';
import { formatDate } from '@/utils/time';
import SkillsMatrix from './skillMatrix';
import CreateSkillsModal from '@/components/skills/createSkillsModal';
import CreateSkillLevelModal from '@/components/skills/createSkillLevelModal';
import SkillsLevelCards from '@/components/skills/skillsLevelCards';
import SkillLevelLegend from '@/components/skills/skillLevelLegend';
import { Input } from '@/components/common/input';
import useFetch from '@/hooks/useFetch';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';

function Skills() {
  const accessToken = useAuthStore((state) => state.accessToken);

  const [createSkillModal, setCreateSkillModal] = useState(false);
  const [createSkillLevelModal, setCreateSkillLevelModal] = useState(false);

  const [deleteTarget, setDeleteTarget] = useState<{
    trainingId?: string;
    userId?: string;
    userName?: string;
    skillLevelId?: string;
    skillLevelTitle?: string;
  } | null>(null);

  const [selectedTrainingId, setSelectedTrainingId] = useState<string>('');
  const [selectedTrainingTitle, setSelectedTrainingTitle] =
    useState<string>('');
  const router = useRouter();
  const [filterKey, setFilterKey] = useState<string>('MATRIX');
  const [activeTab, setActiveTab] = useState<number>(0);
  const [skill, setSkill] = useState(null);
  const [editSkill, setEditSkill] = useState(false);
  const [editingSkillData, setEditingSkillData] = useState<any>(null); // Add this new state

  const [deletingSkill, setDeletingSkill] = useState(false);
  const [deletingSkillData, setDeletingSkillData] = useState<any>(null);

  const {
    data: matrixData,
    isLoading: matrixLoading,
    error: matrixError,
    reFetch: matrixReFetch,
  } = useFetch<{
    records: any[];
  }>(accessToken, 'employee/skills/employees/');

  const {
    data: employeesData,
    isLoading: employeesDataLoading,
    error: employeesDataError,
    reFetch: employeesDataRefetch,
  } = useFetch<{
    records: any[];
  }>(accessToken, 'employees');

  const {
    data: skillLevelData,
    isLoading: skillLevelDataLoading,
    error: skillLevelDataError,
    reFetch: skillLevelDataRefetch,
  } = useFetch<{
    records: any[];
  }>(accessToken, 'employee/skill-levels');

  const {
    data: skillsData,
    isLoading: skillsDataLoading,
    error: skillsDataError,
    reFetch: skillsDataRefetch,
  } = useFetch<{
    records: any[];
  }>(accessToken, 'employee/skills');

  const { deleteData, isLoading: deleteLoading } = useDelete();

  // Handler functions for skills level cards
  const handleEditSkillLevel = (level: any) => {
    toast.info(`Edit skill level: ${level.levelTitle}`);
    // TODO: Implement edit functionality
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteData(accessToken as string, `employee/skills/${id}`); // Changed from skill-levels to skills
      setDeletingSkill(false);
      setDeletingSkillData(null);
      skillsDataRefetch(); // This refetches the skills library data
      toast.success('Skill deleted successfully');
    } catch (error) {
      toast.error('Failed to delete skill');
      console.error('Delete error:', error);
    }
  };
  // ----------------- MOCK DATA -----------------
  const mockTrainings = [
    {
      id: 't1',
      title: 'React Basics',
      description: 'Introduction to React',
      training_type: 'Online',
      duration: '3h',
      status: 'Published',
      assigned_user_count: 5,
      percent_completion: 40,
    },
    {
      id: 't2',
      title: 'Advanced TypeScript',
      description: 'Deep dive into TypeScript',
      training_type: 'Classroom',
      duration: '5h',
      status: 'Draft',
      assigned_user_count: 0,
      percent_completion: 0,
    },
  ];

  const mockAssignments = [
    {
      id: 'a1',
      user: { id: 'u1', name: 'John Doe', employee_id: 'EMP001' },
      training: { id: 't1', title: 'React Basics' },
      assigned_on: '2025-08-01T10:00:00Z',
      due_date: '2025-08-10T10:00:00Z',
      status: 'Ongoing',
      completion: '40%',
      score: 80,
    },
  ];

  // const mockSkillsLibrary = [
  //   {
  //     id: 's1',
  //     name: 'Quality Management Systems',
  //     description: 'Knowledge of ISO 9001 and quality management principles',
  //     category: 'Quality',
  //     level: '3',
  //   },
  //   {
  //     id: 's2',
  //     name: 'Safety Protocols',
  //     description:
  //       'Understanding of workplace safety procedures and regulations',
  //     category: 'Safety',
  //     level: '5',
  //   },
  // ];

  // const mockSkillsLevel = [
  //   {
  //     id: 'l1',
  //     level: '1',
  //     levelTitle: 'Beginner',
  //     levelDescription:
  //       'Basic understanding and knowledge of the skill area. Can perform simple tasks with guidance.',
  //     levelRequirement:
  //       'No prior experience required. Basic training and supervision needed.',
  //   },
  //   {
  //     id: 'l2',
  //     level: '2',
  //     levelTitle: 'Intermediate',
  //     levelDescription:
  //       'Good understanding of the skill area. Can work independently on standard tasks.',
  //     levelRequirement:
  //       'Some experience in the field. Ability to work with minimal supervision.',
  //   },
  //   {
  //     id: 'l3',
  //     level: '3',
  //     levelTitle: 'Advanced',
  //     levelDescription:
  //       'Deep knowledge and expertise in the skill area. Can handle complex tasks and mentor others.',
  //     levelRequirement:
  //       'Significant experience and proven track record. Ability to train and guide others.',
  //   },
  //   {
  //     id: 'l4',
  //     level: '4',
  //     levelTitle: 'Expert',
  //     levelDescription:
  //       'Mastery level understanding. Recognized authority in the field with ability to innovate.',
  //     levelRequirement:
  //       'Extensive experience and leadership capabilities. Ability to develop new methodologies.',
  //   },
  //   {
  //     id: 'l5',
  //     level: '5',
  //     levelTitle: 'Master',
  //     levelDescription:
  //       'Highest level of expertise. Industry thought leader with ability to shape the field.',
  //     levelRequirement:
  //       'Decades of experience and significant contributions to the field. Published work and industry recognition.',
  //   },
  // ];

  // ----------------- TABS -----------------
  const tabsData = [
    {
      name: 'Skills Matrix',
      textColor: 'text-dark-100',
      filter: 'MATRIX',
      onClick: () => setFilterKey('MATRIX'),
    },
    {
      name: 'Skills Library',
      textColor: 'text-dark-100',
      filter: 'LIBRARY',
      onClick: () => setFilterKey('LIBRARY'),
    },
    {
      name: 'Skills Level',
      textColor: 'text-dark-100',
      filter: 'LEVEL',
      onClick: () => setFilterKey('LEVEL'),
    },
  ];

  // ----------------- TABLE COLUMNS -----------------
  const trainingColumns = [
    {
      key: 'title',
      label: 'Title',
      render: (v: any, row: any) => <div>{row.title}</div>,
    },
    {
      key: 'training_type',
      label: 'Type',
      render: (v: any, row: any) => row.training_type,
    },
    { key: 'duration', label: 'Duration' },
    { key: 'status', label: 'Status' },
    {
      key: 'assigned_user_count',
      label: 'Assigned',
      render: (v: any, row: any) =>
        row.assigned_user_count ? `${row.assigned_user_count} employees` : 0,
    },
    {
      key: 'percent_completion',
      label: 'Completion',
      render: (v: any, row: any) =>
        row.percent_completion ? `${row.percent_completion}%` : '0%',
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (v: any, row: any) =>
        row.status === 'Published' ? (
          <button
            onClick={() => {
              setSelectedTrainingId(row.id);
              setSelectedTrainingTitle(row.title);
            }}
            className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
          >
            <UserPlus size={20} />
          </button>
        ) : null,
    },
  ];

  const assignmentColumns = [
    {
      key: 'user',
      label: 'Employee',
      render: (v: any, row: any) => row.user?.name,
    },
    {
      key: 'training.title',
      label: 'Training',
      render: (v: any, row: any) => row.training?.title,
    },
    {
      key: 'assigned_on',
      label: 'Assigned Date',
      render: (v: any) => formatDate(v),
    },
    { key: 'due_date', label: 'Due Date', render: (v: any) => formatDate(v) },
    { key: 'status', label: 'Status' },
    { key: 'completion', label: 'Completion' },
    { key: 'score', label: 'Score' },
    {
      key: 'actions',
      label: 'Actions',
      render: (v: any, row: any) => (
        <button
          onClick={() => {
            toast.info(`Mock unassign ${row.user?.name}`);
            setDeleteTarget(null);
          }}
          className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
        >
          <TrashIcon height={20} />
        </button>
      ),
    },
  ];

  const libraryColumns = [
    {
      key: 'name',
      label: 'Name',
      sortable: true,
      resizable: true,
      width: 500,
      showFilter: false,
      render: (value: any, row: any) => {
        return (
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-primary-400 font-semibold cursor-pointer">
                {row?.name}
              </span>
            </div>
            {row?.description && (
              <span className="text-gray-500 text-xs font-semibold overflow-hidden text-ellipsis">
                {row.description}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'category',
      label: 'Category',
      render: (v: any, row: any) => row.category.name,
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      resizable: true,
      width: 120,
      showFilter: false,
      render: (v: any, row: any) => (
        <div className="flex gap-4">
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setEditingSkillData(row); // Set the specific skill data
              setEditSkill(true); // Open the modal
            }}
            className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
          >
            <Pencil height={20} />
          </button>

          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setDeletingSkillData(row); // Set the skill data
              setDeletingSkill(true); // Open the modal
            }}
            className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
            title="Delete Skill"
          >
            <TrashIcon height={20} />
          </button>
        </div>
      ),
    },
  ];

  const levelColumns = [
    { key: 'skill', label: 'Skill', render: (v: any, row: any) => row.skill },
    {
      key: 'employee',
      label: 'Employee',
      render: (v: any, row: any) => row.employee,
    },
    { key: 'level', label: 'Level', render: (v: any, row: any) => row.level },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      resizable: true,
      width: 120,
      showFilter: false,
      render: (v: any, row: any) => (
        <div className="flex gap-4">
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setDeleteTarget({
                trainingId: row?.training?.id,
                userId: row?.user?.id,
                userName: row?.user?.name,
              });
            }}
            className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
            title="Edit Skill"
          >
            <Pencil height={20} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setDeleteTarget({
                trainingId: row?.training?.id,
                userId: row?.user?.id,
                userName: row?.user?.name,
              });
            }}
            className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
            title="Unassign Training"
          >
            <TrashIcon height={20} />
          </button>
        </div>
      ),
    },
  ];

  const getDataForTab = () => {
    switch (filterKey) {
      case 'ASSIGNMENTS':
        return mockAssignments;
      case 'LIBRARY':
        return skillsData?.records;
      case 'LEVEL':
        return skillLevelData?.records;
      default:
        return mockTrainings;
    }
  };

  const getColumnsForTab = () => {
    switch (filterKey) {
      case 'ASSIGNMENTS':
        return assignmentColumns;
      case 'LIBRARY':
        return libraryColumns;
      case 'LEVEL':
        return levelColumns;
      default:
        return trainingColumns;
    }
  };

  const data = getDataForTab();
  const isLoading = false;

  return (
    <Layout>
      <div>
        <div className="flex flex-col my-5">
          <Breadcrumb
            data={[
              { name: 'People hub', link: '/people' },
              { name: 'Skills Management', link: '#' },
            ]}
          />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Skills Management
          </div>
          <div className="text-dark-100 font-normal text-base leading-8">
            Manage skills matrix, library, and skill levels
          </div>
        </div>

        <Tabs
          tabsData={tabsData}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />

        {/* ✅ Show SkillsMatrix for MATRIX tab */}
        {filterKey === 'MATRIX' ? (
          <SkillsMatrix
            skillsData={matrixData?.records ?? []}
            skillCategories={skillsData?.records ?? []}
            skillLevels={skillLevelData?.records ?? []}
            employeesData={employeesData?.records ?? []}
          />
        ) : filterKey === 'LEVEL' ? (
          <div>
            <div className="flex justify-between items-center mt-4 mb-4">
              <Input
                placeholder="Search"
                className="w-[40vw] bg-white flex-auto rounded-lg border border-grey-100 py-2.5 px-3 text-black outline-none transition focus:border-primary active:border-primary"
                // onChange={(e) => {
                //   setSearch(e.target.value);
                // }}
                // value={search}
              />
              <Dialog
                open={createSkillLevelModal}
                onOpenChange={setCreateSkillLevelModal}
              >
                <DialogTrigger asChild>
                  <PrimaryButton
                    size="medium"
                    text="Add Level"
                    icon={<Plus size={20} />}
                    iconPosition="left"
                  />
                </DialogTrigger>
                <CreateSkillLevelModal
                  open={createSkillLevelModal}
                  onOpenChange={setCreateSkillLevelModal}
                  reFetch={skillLevelDataRefetch}
                />
              </Dialog>
            </div>
            <SkillsLevelCards
              data={skillLevelData?.records ?? []}
              onEdit={handleEditSkillLevel}
              reFetch={skillLevelDataRefetch}
            />
          </div>
        ) : (
          <AdvancedTable
            data={data ?? []}
            columns={getColumnsForTab()}
            loading={isLoading}
            sortable
            resizable
            pagination
            searchRightSideElement={
              filterKey === 'LIBRARY' ? (
                <div className="flex gap-4">
                  <Dialog
                    open={createSkillModal}
                    onOpenChange={setCreateSkillModal}
                  >
                    <DialogTrigger asChild>
                      <PrimaryButton
                        size="medium"
                        text="Add Skill"
                        icon={<Plus size={20} />}
                        iconPosition="left"
                      />
                    </DialogTrigger>
                    <CreateSkillsModal
                      open={createSkillModal}
                      onOpenChange={setCreateSkillModal}
                      reFetch={skillsDataRefetch}
                    />
                  </Dialog>
                </div>
              ) : undefined
            }
          />
        )}

        {/* Delete Modal */}
        <Dialog
          open={!!deleteTarget}
          onOpenChange={(open) => !open && setDeleteTarget(null)}
        >
          <DeleteModal
            title={
              deleteTarget?.skillLevelId
                ? 'Delete Skill Level'
                : 'Delete Assignment'
            }
            infoText={
              deleteTarget?.skillLevelId
                ? `Are you sure you want to delete the skill level "${deleteTarget?.skillLevelTitle}"?`
                : `Are you sure you want to unassign this training to ${deleteTarget?.userName}?`
            }
            btnText="Delete"
            onClick={() => {
              if (deleteTarget?.skillLevelId) {
                toast.info(
                  `Mock delete skill level: ${deleteTarget.skillLevelTitle}`,
                );
              } else {
                toast.info('Mock unassign');
              }
              setDeleteTarget(null);
            }}
            dialogContentClass="min-w-[33.375rem]"
            btnLoading={false}
          />
        </Dialog>

        <Dialog
          open={deletingSkill}
          onOpenChange={(open) => {
            if (!open) {
              setDeletingSkill(false);
              setDeletingSkillData(null);
            }
          }}
        >
          {deletingSkill && deletingSkillData && (
            <DeleteModal
              title="Delete Skill"
              infoText={`Are you sure you want to delete "${deletingSkillData?.name}"?`}
              btnText="Delete"
              btnLoading={deleteLoading} // Use the correct loading state
              onClick={() => handleDelete(deletingSkillData?.id)}
              dialogContentClass="min-w-[33.375rem]"
            />
          )}
        </Dialog>

        <Dialog open={editSkill} onOpenChange={setEditSkill}>
          <CreateSkillsModal
            open={editSkill}
            onOpenChange={setEditSkill}
            edit
            skillData={editingSkillData ?? undefined}
            skillId={editingSkillData?.id}
            reFetch={skillsDataRefetch}
            setOpenEdit={setEditSkill}
          />
        </Dialog>
      </div>
    </Layout>
  );
}

export default Skills;
