import React from 'react';
import { Pencil, TrashIcon } from 'lucide-react';
import { getSkillLevelColor } from '@/utils/skillLevelColors';

interface SkillLevel {
  id: string;
  level: string;
  levelTitle: string;
  levelDescription: string;
  levelRequirement: string;
}

interface SkillsLevelCardsProps {
  data: SkillLevel[];
  onEdit: (level: SkillLevel) => void;
  onDelete: (level: SkillLevel) => void;
}

const SkillsLevelCards: React.FC<SkillsLevelCardsProps> = ({
  data,
  onEdit,
  onDelete,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {data.map((level) => {
        const colors = getSkillLevelColor(level.level);
        return (
          <div
            key={level.id}
            className={`relative p-6 rounded-lg border bg-white transition-shadow duration-200`}
          >
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center gap-3">
                <div className={`px-3 pb-1 ${colors.bg} rounded-full`}>
                  <span className={`text-white font-semibold text-xs`}>
                    Level {level.level}
                  </span>
                </div>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => onEdit(level)}
                  className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                  title="Edit Level"
                >
                  <Pencil size={16} className="text-gray-600" />
                </button>
                <button
                  onClick={() => onDelete(level)}
                  className="w-8 h-8 bg-red-50 rounded-full flex items-center justify-center hover:bg-red-100 transition-colors"
                  title="Delete Level"
                >
                  <TrashIcon size={16} className="text-red-500" />
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-dark-300">
                {level.levelTitle}
              </h3>
              <div>
                <p className="text-sm text-gray-700 leading-relaxed">
                  {level.levelDescription}
                </p>
              </div>
              <div>
                <ul className="text-sm text-gray-700 space-y-1">
                  {level.levelRequirement
                    .split('.')
                    .filter((item) => item.trim())
                    .map((requirement, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span
                          className={`w-1.5 h-1.5 bg-gray-700 rounded-full mt-2 flex-shrink-0`}
                        ></span>
                        <span className="leading-relaxed">
                          {requirement.trim()}
                        </span>
                      </li>
                    ))}
                </ul>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default SkillsLevelCards;
