import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { useAuthStore } from '@/globalProvider/authStore';

interface CreateSkillLevelModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trainingId?: string;
  trainingData?: unknown;
  refetch?: () => void;
}

const CreateSkillLevelModal: React.FC<CreateSkillLevelModalProps> = ({
  open,
  onOpenChange,
}) => {
  useAuthStore((state) => state.accessToken);

  const [levelName, setLevelName] = useState('');
  const [description, setDescription] = useState('');
  const [levelNumber, setLevelNumber] = useState<string>('');
  const [requirements, setRequirements] = useState('');

  useEffect(() => {
    if (open) {
      setLevelName('');
      setDescription('');
      setLevelNumber('');
      setRequirements('');
    }
  }, [open]);

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Skill Level</DialogTitle>
        </DialogHeader>

        <div className="space-y-5 mt-2">
          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="level_name"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Level Name
            </Label>
            <Input
              id="level_name"
              placeholder="Enter level name (e.g., Intermediate)"
              value={levelName}
              onChange={(e) => setLevelName(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="flex flex-col gap-2.5">
              <Label
                htmlFor="level_number"
                className="text-base font-medium leading-6 text-dark-100"
              >
                Level Number
              </Label>
              <Input
                id="level_number"
                type="number"
                min={1}
                max={99}
                placeholder="e.g., 1"
                value={levelNumber}
                onChange={(e) => setLevelNumber(e.target.value)}
              />
            </div>
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="description"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Enter description"
              rows={3}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="requirements"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Requirements
            </Label>
            <Textarea
              id="requirements"
              placeholder="List requirements for this level"
              rows={4}
              value={requirements}
              onChange={(e) => setRequirements(e.target.value)}
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t mt-6">
          <SecondaryButton text="Cancel" size="medium" onClick={handleClose} />
          <PrimaryButton text="Save" size="medium" onClick={handleSave} />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateSkillLevelModal;
