import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';

interface Category {
  id: string;
  name: string;
}

interface DepartmentsResponse {
  records: Category[];
}

interface CreateSkillsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trainingId?: string;
  trainingData?: unknown;
  refetch?: () => void;
}

const LEVELS = ['L1', 'L2', 'L3', 'L4', 'L5'];

const CreateSkillsModal: React.FC<CreateSkillsModalProps> = ({
  open,
  onOpenChange,
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);

  const { data: departments } = useFetch<DepartmentsResponse>(
    accessToken,
    'departments',
    {},
  );

  const [skillName, setSkillName] = useState('');
  const [departmentId, setDepartmentId] = useState('');
  const [currentLevel, setCurrentLevel] = useState('');
  const [targetLevel, setTargetLevel] = useState('');
  const [description, setDescription] = useState('');

  useEffect(() => {
    if (open) {
      setSkillName('');
      setDepartmentId('');
      setCurrentLevel('');
      setTargetLevel('');
      setDescription('');
    }
  }, [open]);

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Skill</DialogTitle>
        </DialogHeader>

        <div className="space-y-5 mt-2">
          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="skill_name"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Skill Name
            </Label>
            <Input
              id="skill_name"
              placeholder="Enter skill name"
              value={skillName}
              onChange={(e) => setSkillName(e.target.value)}
            />
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="department"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Department
            </Label>
            <Select value={departmentId} onValueChange={setDepartmentId}>
              <SelectTrigger id="department">
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                {departments?.records?.map((dept) => (
                  <SelectItem key={dept.id} value={dept.id}>
                    {dept.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="flex flex-col gap-2.5">
              <Label
                htmlFor="current_level"
                className="text-base font-medium leading-6 text-dark-100"
              >
                Current Skill Level
              </Label>
              <Select value={currentLevel} onValueChange={setCurrentLevel}>
                <SelectTrigger id="current_level">
                  <SelectValue placeholder="Select current level" />
                </SelectTrigger>
                <SelectContent>
                  {LEVELS.map((lvl) => (
                    <SelectItem key={lvl} value={lvl}>
                      {lvl}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col gap-2.5">
              <Label
                htmlFor="target_level"
                className="text-base font-medium leading-6 text-dark-100"
              >
                Target Skill Level
              </Label>
              <Select value={targetLevel} onValueChange={setTargetLevel}>
                <SelectTrigger id="target_level">
                  <SelectValue placeholder="Select target level" />
                </SelectTrigger>
                <SelectContent>
                  {LEVELS.map((lvl) => (
                    <SelectItem key={lvl} value={lvl}>
                      {lvl}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="description"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Enter description"
              rows={3}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t mt-6">
          <SecondaryButton text="Cancel" size="medium" onClick={handleClose} />
          <PrimaryButton text="Save" size="medium" onClick={handleSave} />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateSkillsModal;
